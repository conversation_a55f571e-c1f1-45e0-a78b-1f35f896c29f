@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced CSS Variables for Dynamic Theming */
:root {
  --color-primary: 20 184 166; /* teal-500 */
  --color-primary-dark: 15 118 110; /* teal-700 */
  --color-success: 34 197 94; /* green-500 */
  --color-error: 239 68 68; /* red-500 */
  --color-warning: 245 158 11; /* amber-500 */
  --color-info: 59 130 246; /* blue-500 */

  /* Light theme colors */
  --bg-primary: 255 255 255; /* white */
  --bg-secondary: 249 250 251; /* gray-50 */
  --bg-tertiary: 243 244 246; /* gray-100 */
  --text-primary: 17 24 39; /* gray-900 */
  --text-secondary: 75 85 99; /* gray-600 */
  --text-tertiary: 156 163 175; /* gray-400 */
  --border-primary: 229 231 235; /* gray-200 */
  --border-secondary: 209 213 219; /* gray-300 */

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  /* Easing functions */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Dark theme colors */
.dark {
  --bg-primary: 17 24 39; /* gray-900 */
  --bg-secondary: 31 41 55; /* gray-800 */
  --bg-tertiary: 55 65 81; /* gray-700 */
  --text-primary: 249 250 251; /* gray-50 */
  --text-secondary: 209 213 219; /* gray-300 */
  --text-tertiary: 156 163 175; /* gray-400 */
  --border-primary: 55 65 81; /* gray-700 */
  --border-secondary: 75 85 99; /* gray-600 */
}

/* Enhanced base styles with dark mode support */
@layer base {
  html {
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
    /* Smooth scrolling */
    scroll-behavior: smooth;
  }

  body {
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Enhanced font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Transition for theme changes */
    transition: background-color var(--duration-normal) var(--ease-out),
                color var(--duration-normal) var(--ease-out);
    /* Default theme colors */
    background-color: rgb(var(--bg-primary));
    color: rgb(var(--text-primary));
  }

  /* Improve touch targets */
  button, [role="button"] {
    touch-action: manipulation;
  }

  /* Enhanced focus styles */
  *:focus-visible {
    outline: 2px solid rgb(var(--color-primary));
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Smooth transitions for interactive elements */
  button, input, select, textarea {
    transition: all var(--duration-fast) var(--ease-out);
  }

  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgb(243 244 246); /* gray-100 */
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(156 163 175); /* gray-400 */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128); /* gray-500 */
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar-track {
    background: rgb(31 41 55); /* gray-800 */
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgb(75 85 99); /* gray-600 */
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128); /* gray-500 */
  }
}

@layer utilities {
  /* Enhanced touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Safe area padding for mobile devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent text selection on UI elements */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }

  /* Enhanced animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out);
  }

  .animate-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-out);
  }

  .animate-slide-down {
    animation: slideDown var(--duration-normal) var(--ease-out);
  }

  .animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-bounce);
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Gradient utilities */
  .gradient-primary {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
  }

  .gradient-success {
    background: linear-gradient(135deg, rgb(var(--color-success)), rgb(34 197 94));
  }

  .gradient-error {
    background: linear-gradient(135deg, rgb(var(--color-error)), rgb(220 38 38));
  }

  /* Enhanced shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.3);
  }

  .shadow-glow-success {
    box-shadow: 0 0 20px rgba(var(--color-success), 0.3);
  }

  .shadow-glow-error {
    box-shadow: 0 0 20px rgba(var(--color-error), 0.3);
  }

  /* Enhanced dark mode utilities */
  .bg-theme-primary {
    background-color: rgb(var(--bg-primary));
  }

  .bg-theme-secondary {
    background-color: rgb(var(--bg-secondary));
  }

  .bg-theme-tertiary {
    background-color: rgb(var(--bg-tertiary));
  }

  .text-theme-primary {
    color: rgb(var(--text-primary));
  }

  .text-theme-secondary {
    color: rgb(var(--text-secondary));
  }

  .text-theme-tertiary {
    color: rgb(var(--text-tertiary));
  }

  .border-theme-primary {
    border-color: rgb(var(--border-primary));
  }

  .border-theme-secondary {
    border-color: rgb(var(--border-secondary));
  }

  /* Enhanced button styles for dark mode */
  .btn-primary {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
    color: white;
    border: none;
    transition: all var(--duration-fast) var(--ease-out);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--color-primary), 0.4);
  }

  .btn-secondary {
    background-color: rgb(var(--bg-secondary));
    color: rgb(var(--text-primary));
    border: 1px solid rgb(var(--border-primary));
    transition: all var(--duration-fast) var(--ease-out);
  }

  .btn-secondary:hover {
    background-color: rgb(var(--bg-tertiary));
    border-color: rgb(var(--border-secondary));
  }

  /* Enhanced card styles */
  .card {
    background-color: rgb(var(--bg-primary));
    border: 1px solid rgb(var(--border-primary));
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all var(--duration-normal) var(--ease-out);
  }

  .card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .dark .card {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .dark .card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
  }
}

/* Markdown content improvements */
@layer components {
  /* Better text wrapping for markdown content */
  .markdown-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Inline code wrapping */
  .markdown-content code {
    word-break: break-word;
    white-space: pre-wrap;
    background-color: rgb(243 244 246); /* gray-100 */
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
  }

  /* Dark mode inline code */
  .dark .markdown-content code {
    background-color: rgb(55 65 81); /* gray-700 */
    color: rgb(229 231 235); /* gray-200 */
  }

  /* Ensure lists wrap properly */
  .markdown-content li {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Enhanced code block styling */
  .markdown-content pre {
    background-color: rgb(31 41 55); /* gray-800 */
    color: rgb(229 231 235); /* gray-200 */
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
  }

  .dark .markdown-content pre {
    background-color: rgb(17 24 39); /* gray-900 */
  }

  /* Enhanced blockquote styling */
  .markdown-content blockquote {
    border-left: 4px solid rgb(var(--color-primary));
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: rgb(var(--text-secondary));
  }

  /* Enhanced table styling */
  .markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
  }

  .markdown-content th,
  .markdown-content td {
    border: 1px solid rgb(var(--border-primary));
    padding: 0.5rem;
    text-align: left;
  }

  .markdown-content th {
    background-color: rgb(var(--bg-secondary));
    font-weight: 600;
  }
}

.clip-path-message-tail {
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}
.clip-path-message-tail-user {
  clip-path: polygon(100% 0, 0 0, 100% 100%);
}

/* Enhanced keyframe animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(var(--color-primary), 0.5); }
  50% { box-shadow: 0 0 20px rgba(var(--color-primary), 0.8); }
}

/* Legacy animation class for backward compatibility */
.animate-fadeIn {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve scrolling performance on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure minimum touch target size for important buttons only */
  .touch-target-large {
    min-height: 44px;
    min-width: 44px;
  }

  /* Fix button sizing issues on mobile */
  button {
    box-sizing: border-box;
  }
}
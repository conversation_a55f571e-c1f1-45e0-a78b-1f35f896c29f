import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import VPSAdminChat from './VPSAdminChat';
import { useTheme } from './hooks/useTheme';

// Theme wrapper component to handle dark mode
const ThemeWrapper = () => {
  const { effectiveTheme } = useTheme();

  return (
    <div className={effectiveTheme === 'dark' ? 'dark' : ''}>
      <div className="flex items-center justify-center h-screen lg:min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-900 dark:to-gray-800 p-0 lg:p-4 overflow-hidden lg:overflow-auto transition-colors duration-300">
        <VPSAdminChat />
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeWrapper />
  </StrictMode>,
)

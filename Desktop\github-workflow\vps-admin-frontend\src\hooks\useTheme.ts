/**
 * Theme management hook for VPS Admin Chat
 * Handles auto theme detection and system preference changes
 */

import { useEffect, useState } from 'react';
import { useAppStore } from '../store/appStore';

export const useTheme = () => {
  const { theme, setTheme } = useAppStore();
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>('light');

  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const updateSystemTheme = (e: MediaQueryListEvent | MediaQueryList) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    // Set initial system theme
    updateSystemTheme(mediaQuery);

    // Listen for system theme changes
    mediaQuery.addEventListener('change', updateSystemTheme);

    return () => {
      mediaQuery.removeEventListener('change', updateSystemTheme);
    };
  }, []);

  // Calculate effective theme based on user preference and system theme
  useEffect(() => {
    let newEffectiveTheme: 'light' | 'dark';
    
    if (theme === 'auto') {
      newEffectiveTheme = systemTheme;
    } else {
      newEffectiveTheme = theme;
    }
    
    setEffectiveTheme(newEffectiveTheme);
    
    // Apply theme to document root
    const root = document.documentElement;
    if (newEffectiveTheme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme, systemTheme]);

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('auto');
    } else {
      setTheme('light');
    }
  };

  const getThemeIcon = () => {
    if (theme === 'auto') {
      return 'Monitor';
    } else if (theme === 'light') {
      return 'Sun';
    } else {
      return 'Moon';
    }
  };

  const getThemeLabel = () => {
    if (theme === 'auto') {
      return `Auto (${systemTheme})`;
    } else {
      return theme.charAt(0).toUpperCase() + theme.slice(1);
    }
  };

  return {
    theme,
    systemTheme,
    effectiveTheme,
    setTheme,
    toggleTheme,
    getThemeIcon,
    getThemeLabel,
  };
};
